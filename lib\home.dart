import 'package:bibl/controllers/heart_controller.dart';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/res/style.dart';
import 'package:bibl/services/reward_service.dart';
import 'package:bibl/utils/performance_utils.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:get/get.dart';
import 'controllers/lesson_controller.dart';
import 'controllers/quiz_controller.dart';

import 'widgets/optimized_merged_items_list.dart';
import 'widgets/premium_widgets.dart';

class Home extends StatefulWidget {
  final ScrollController scrollController;
  const Home({super.key, required this.scrollController});

  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home> with AutomaticKeepAliveClientMixin {
  final ProfileController profileController = Get.find();
  final HeartController heartController = Get.find();
  final QuizController quizController = Get.find();
  final LessonController lessonController = Get.find();

  String weekDaysSelected = 'Pon';
  int streak = 0;
  bool _isInitialized = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    // Defer initialization to not block UI
    SchedulerBinding.instance.addPostFrameCallback((_) {
      _initializeHome();
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _initializeHome() async {
    await PerformanceUtils.measureOperation(
      'Home Initialization',
      () async {
        // Check if data is ready
        if (profileController.userr.value.uid != null && !_isInitialized) {
          // Perform streak checks in background
          _performStreakChecks();

          // Mark as initialized
          setState(() {
            _isInitialized = true;
          });
        }
      },
    );
  }

  /// Perform streak checks without affecting loading state
  Future<void> _performStreakChecks() async {
    try {
      // Perform both streak checks in parallel
      await Future.wait([
        checkWeeklyStreak(),
        checkConsecutiveStreak(),
      ]);
    } catch (e) {
      // Handle any errors gracefully
      debugPrint('Error during streak checks: $e');
    }
  }

  Future<void> checkWeeklyStreak() async {
    // No delay needed - ProfileController should be ready
    DateTime now = DateTime.now();
// Check if it's a new day based on 22:00 to 6:00 logic
    DateTime startOfNewDay = DateTime(now.year, now.month, now.day, 6);
    DateTime endOfNewDay = DateTime(now.year, now.month, now.day, 22);
    if (now.isAfter(startOfNewDay) || now.isBefore(endOfNewDay)) {
      // Fetch last streak update date (default to 1 day before today)
      DateTime lastUpdateDate =
          profileController.userr.value.lastStreakUpdate?.toDate() ??
              now.subtract(const Duration(days: 1));

      // Check if the streak was already updated today
      if (now.difference(lastUpdateDate).inDays == 0) {
        return;
      }
      // Initialize reward service
      RewardService rewardService =
          RewardService(profileController.userr.value.uid ?? '');

      // Update streak logic
      int currentStreak = profileController.userr.value.weeklyStreak ?? 0;
      int currentHearts = profileController.userr.value.hearts ?? 0;

      if (now.difference(lastUpdateDate).inDays == 1) {
        // User logged in on the next consecutive day: Increment streak
        currentStreak += 1;

        // If streak exceeds 7, reset it
        if (currentStreak > 7) {
          currentStreak = 1;
        }

        // Handle Neuron Rewards for streak milestones
        if (currentStreak == 1) {
          currentHearts += heartController.dailyHearts.value;
        } else if (currentStreak == 2) {
          rewardService.addNeurons(
            50,
          ); // 50 neurons for 2-day streak
          currentHearts += heartController.dailyHearts.value;
        } else if (currentStreak == 3) {
          rewardService.dailyLearner(); // 100 neurons for 3-day streak
          currentHearts += 2;
        } else if (currentStreak == 4) {
          rewardService.addNeurons(150); // 150 neurons for 4-day streak
          currentHearts += 3;
        } else if (currentStreak == 5) {
          rewardService.addNeurons(200); // 200 neurons for 5-day streak
          currentHearts += 4;
        } else if (currentStreak == 6) {
          rewardService.addNeurons(250); // 250 neurons for 6-day streak
          currentHearts += 5;
        } else if (currentStreak == 7) {
          rewardService.weeklyStreak(); // 300 neurons for 7-day streak
          currentHearts += 6;
        }
      } else {
        // User missed a day: Reset streak to 1
        currentStreak = 1;
        currentHearts += heartController.dailyHearts.value;
      }

      // Update local values
      profileController.userr.value.weeklyStreak = currentStreak;
      profileController.userr.value.hearts = currentHearts;
      profileController.userr.value.lastStreakUpdate = Timestamp.now();

      // Sync updated streak and timestamp to Firestore
      await profileController.updateHearts(currentHearts);
      await profileController.updateStreak(currentStreak);
    }
  }

  Future<void> checkConsecutiveStreak() async {
    // No delay needed - execute immediately
    DateTime now = DateTime.now();
// Check if it's a new day based on 22:00 to 6:00 logic
    DateTime startOfNewDay = DateTime(now.year, now.month, now.day, 6);
    DateTime endOfNewDay = DateTime(now.year, now.month, now.day, 22);
    if (now.isAfter(startOfNewDay) || now.isBefore(endOfNewDay)) {
      int consecutiveStreak =
          profileController.userr.value.consecutiveStreak ?? 0;
      RewardService rewardService =
          RewardService(profileController.userr.value.uid ?? '');
      DateTime lastActiveDate =
          profileController.userr.value.lastActiveTime?.toDate() ??
              DateTime.now().subtract(const Duration(days: 1));

      // Check if the streak has already been updated today
      if (now.difference(lastActiveDate).inDays == 0) {
        // Streak has already been updated today, no further action required
        return;
      }
      // Check if the user logged in on the next day
      if (now.difference(lastActiveDate).inDays == 1) {
        // User logged in on the next day (consecutive streak continues)
        consecutiveStreak += 1;
        profileController.userr.value.consecutiveStreak = consecutiveStreak;
        profileController.userr.value.lastActiveTime = Timestamp.now();
        profileController.updateConsecutiveStreak(consecutiveStreak);
        // Consecutive Streak Milestone Rewards
        if (consecutiveStreak >= 30) {
          rewardService.streakChampion();
        }
        if (consecutiveStreak >= 60) {
          rewardService.learningAddict();
        }
        if (consecutiveStreak >= 90 && consecutiveStreak % 90 == 0) {
          // Loyal User reward every 90 days (recurring)
          rewardService.loyalUser();
        }
      } else {
        debugPrint('streakkkkkkkkkkkkkk not');
        // If not logged in on consecutive days, reset consecutive streak to 1
        consecutiveStreak = 1;
        profileController.userr.value.consecutiveStreak = consecutiveStreak;
        profileController.userr.value.lastActiveTime = Timestamp.now();
        profileController.updateConsecutiveStreak(consecutiveStreak);
      }

      setState(() {}); // Update UI to reflect the current consecutive streak
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      body: Obx(() {
        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 600),
          switchInCurve: Curves.easeInOut,
          switchOutCurve: Curves.easeInOut,
          transitionBuilder: (Widget child, Animation<double> animation) {
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
          child: profileController.isUserDataLoading.value
              ? Container(
                  key: const ValueKey('loading'),
                  color: Colors.white,
                  child: Center(
                    child: RepaintBoundary(
                      child: Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(30),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 10,
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                        child: const CupertinoActivityIndicator(
                          radius: 15,
                          animating: true,
                        ),
                      ),
                    ),
                  ),
                )
              : Column(
                  key: const ValueKey('content'),
                  children: [
                    _buildHomeHeader(),
                    Expanded(
                      child: OptimizedMergedItemsList(
                        isForLibrary: false,
                        scrollController: widget.scrollController,
                      ),
                    ),
                  ],
                ),
        );
      }),
    );
  }

  Widget _buildHomeHeader() {
    return RepaintBoundary(
      child: Container(
        height: 175,
        decoration: BoxDecoration(
          gradient: mainColorsGradient,
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(18),
            bottomRight: Radius.circular(18),
          ),
          boxShadow: const [
            BoxShadow(
              color: Colors.black26,
              offset: Offset(0, 4),
              blurRadius: 10,
            ),
          ],
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.fromLTRB(16, 20, 16, 5),
            child: Column(
              children: [
                const Spacer(flex: 4),
                _buildHeaderRow(),
                const Spacer(flex: 3),
                _buildDaysWidget(),
                const Spacer(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderRow() {
    return Obx(() => Row(
          children: [
            // Logo/App name
            const Text(
              'UmniLab',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            // User stats icons
            profileController.isPremiumUser.value
                ? PremiumCard(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    backgroundColor: Colors.white.withValues(alpha: 0.2),
                    borderRadius: 12,
                    elevation: 2,
                    animationDuration: const Duration(milliseconds: 150),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(Icons.favorite, color: Colors.white, size: 16),
                        SizedBox(width: 4),
                        Icon(Icons.all_inclusive,
                            color: Colors.white, size: 16),
                      ],
                    ),
                  )
                : _buildStatIcon(
                    icon: Icons.favorite,
                    value: '${profileController.userr.value.hearts ?? 0}',
                  ),
            const SizedBox(width: 10),
            _buildStatIcon(
              icon: Icons.psychology,
              value: '${profileController.userr.value.neurons ?? 0}',
            ),
            const SizedBox(width: 10),
            _buildStatIcon(
              icon: Icons.emoji_events,
              value:
                  '${profileController.userr.value.achievements?.length ?? 0}',
            ),
          ],
        ));
  }

  Widget _buildStatIcon({required IconData icon, required String value}) {
    return PremiumCard(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      backgroundColor: Colors.white.withValues(alpha: 0.2),
      borderRadius: 12,
      elevation: 2,
      animationDuration: const Duration(milliseconds: 150),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: Colors.white, size: 16),
          const SizedBox(width: 4),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDaysWidget() {
    List<String> weekDays = [
      'Pon', // Monday
      'Uto', // Tuesday
      'Sre', // Wednesday
      'Čet', // Thursday
      'Pet', // Friday
      'Sub', // Saturday
      'Ned' // Sunday
    ];

    int todayIndex = DateTime.now().weekday - 1; // Monday = 0, Sunday = 6
    int streakLength = profileController.userr.value.weeklyStreak ?? 0;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: List.generate(7, (index) {
        String title = weekDays[index];
        bool isToday = index == todayIndex;

        // Calculate if this day should show as completed in the streak
        bool isStreakDay = false;
        if (streakLength > 0) {
          // Calculate how many days back from today the streak goes
          int streakStartIndex = (todayIndex + 1 - streakLength) % 7;
          if (streakStartIndex < 0) streakStartIndex += 7;

          // Check if current index is within the streak range
          if (streakStartIndex <= todayIndex) {
            // Normal case: streak doesn't wrap around the week
            isStreakDay = index >= streakStartIndex && index <= todayIndex;
          } else {
            // Wrap around case: streak goes from end of week to beginning
            isStreakDay = index >= streakStartIndex || index <= todayIndex;
          }
        }

        return _weekDayTitleWidget(title, isStreakDay, isToday);
      }),
    );
  }

  Widget _weekDayTitleWidget(String title, bool isStreakDay, bool isToday) {
    return Column(
      children: [
        Container(
          height: isToday ? 20 : 18, // Larger size for today
          width: isToday ? 20 : 18,
          decoration: BoxDecoration(
            color: isStreakDay ? Colors.white : Colors.transparent,
            shape: BoxShape.circle,
            border: Border.all(
              color:
                  isToday ? Colors.white : Colors.white.withValues(alpha: 0.5),
              width: isToday ? 2 : 1, // Much thicker border for today
            ),
            // Add glow effect for today
            boxShadow: isToday
                ? [
                    BoxShadow(
                      color: Colors.white.withValues(alpha: 0.6),
                      blurRadius: 8,
                      spreadRadius: 2,
                    ),
                  ]
                : null,
          ),
          child: isStreakDay
              ? Icon(
                  Icons.check,
                  color: mainColor,
                  size: isToday ? 13 : 12, // Larger icon for today
                )
              : null,
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: TextStyle(
            color: isToday ? Colors.white : Colors.white.withValues(alpha: 0.8),
            fontSize: isToday ? 13 : 12, // Larger font for today
            fontWeight: isToday
                ? FontWeight.w900
                : FontWeight.w500, // Much bolder for today
            shadows: isToday
                ? [
                    const Shadow(
                      color: Colors.black26,
                      offset: Offset(0, 1),
                      blurRadius: 2,
                    ),
                  ]
                : null, // Add text shadow for today
          ),
        ),
      ],
    );
  }
}
