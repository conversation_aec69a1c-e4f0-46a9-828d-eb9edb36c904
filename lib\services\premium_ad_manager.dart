import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:get/get.dart';

/// Centralized ad management service for optimal performance
class PremiumAdManager extends GetxController {
  static PremiumAdManager get to => Get.find();

  // Ad states tracking - no caching of BannerAd instances to avoid reuse issues
  final Map<String, AdLoadState> _adStates = {};
  final Map<String, DateTime> _lastLoadAttempt = {};
  final Map<String, int> _retryCount = {};
  final Map<String, StreamController<BannerAd?>> _adStreamControllers = {};

  // Configuration
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 3);
  static const Duration adRefreshInterval = Duration(minutes: 5);

  // Track initialization
  bool _isInitialized = false;
  final _initCompleter = Completer<void>();

  // Prevent scroll jumps by tracking scroll state
  bool _isScrolling = false;
  Timer? _scrollEndTimer;

  // Test ad unit IDs for development
  final Map<String, String> _testAdUnits = {
    'android': 'ca-app-pub-3940256099942544/6300978111',
    'ios': 'ca-app-pub-3940256099942544/2934735716',
  };

  // Production ad unit IDs
  final Map<String, String> _androidAdUnits = {
    'home_banner_1': 'ca-app-pub-8639821055582439/1096152024',
    'home_banner_2': 'ca-app-pub-8639821055582439/9477204925',
    'home_banner_3': 'ca-app-pub-8639821055582439/4156563660',
    'home_banner_default': 'ca-app-pub-8639821055582439/8056296139',
  };

  final Map<String, String> _iosAdUnits = {
    'home_banner_1': 'ca-app-pub-8639821055582439/3769550858',
    'home_banner_2': 'ca-app-pub-8639821055582439/4265224933',
    'home_banner_3': 'ca-app-pub-8639821055582439/1746888714',
    'home_banner_default': 'ca-app-pub-8639821055582439/2021354915',
  };

  @override
  void onInit() {
    super.onInit();
    _initializeAds();
  }

  /// Initialize Mobile Ads SDK
  Future<void> _initializeAds() async {
    try {
      debugPrint('🚀 Initializing Mobile Ads SDK...');
      await MobileAds.instance.initialize();

      // Update request configuration for better fill rate
      await MobileAds.instance.updateRequestConfiguration(
        RequestConfiguration(
          testDeviceIds: kDebugMode ? ['YOUR_TEST_DEVICE_ID'] : [],
          maxAdContentRating: MaxAdContentRating.g,
          tagForChildDirectedTreatment:
              TagForChildDirectedTreatment.unspecified,
        ),
      );

      _isInitialized = true;
      _initCompleter.complete();
      debugPrint('✅ Mobile Ads SDK initialized successfully');

      // Start periodic refresh
      _startPeriodicRefresh();
    } catch (e) {
      debugPrint('❌ Failed to initialize Mobile Ads SDK: $e');
      _isInitialized = false;
      if (!_initCompleter.isCompleted) {
        _initCompleter.completeError(e);
      }
    }
  }

  /// Wait for initialization to complete
  Future<void> ensureInitialized() async {
    if (_isInitialized) return;
    try {
      await _initCompleter.future.timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw TimeoutException('Ad initialization timeout');
        },
      );
    } catch (e) {
      debugPrint('⚠️ Ad initialization failed or timed out: $e');
    }
  }

  /// Get ad unit ID based on position and platform
  String getAdUnitId(String key) {
    // Use test ads in debug mode
    if (kDebugMode) {
      return Platform.isAndroid
          ? _testAdUnits['android']!
          : _testAdUnits['ios']!;
    }

    // Rotate through different ad units for better fill rate
    final adUnits = Platform.isAndroid ? _androidAdUnits : _iosAdUnits;
    final keyNum = key.hashCode % 3;
    final adKey = 'home_banner_${keyNum + 1}';
    return adUnits[adKey] ?? adUnits['home_banner_default']!;
  }

  /// Get appropriate ad size based on container dimensions
  AdSize _getOptimalAdSize(double width, double height) {
    // Use square ad format for better visual impact
    // Medium Rectangle (300x250) provides a good square-like format
    // that works well for most content and has good fill rates

    // For smaller containers, use a smaller square format
    if (width < 280 || height < 200) {
      return AdSize.banner; // 320x50 - fallback for small containers
    }

    // For medium to large containers, use medium rectangle (300x250)
    // This is close to square and provides good ad performance
    return AdSize.mediumRectangle; // 300x250 - square-like format
  }

  /// Get stream for ad updates
  Stream<BannerAd?> getAdStream(String key) {
    if (!_adStreamControllers.containsKey(key)) {
      _adStreamControllers[key] = StreamController<BannerAd?>.broadcast();
    }
    return _adStreamControllers[key]!.stream;
  }

  /// Load ad for a specific key
  Future<void> loadAd(
    String key, {
    required Size size,
    String? customAdUnitId,
  }) async {
    // Ensure ads are initialized
    await ensureInitialized();

    // Check if already loading
    if (_adStates[key] == AdLoadState.loading) {
      debugPrint('⏳ Ad already loading for key: $key');
      return;
    }

    // Check retry count
    final retries = _retryCount[key] ?? 0;
    if (retries >= maxRetries) {
      final lastAttempt = _lastLoadAttempt[key];
      if (lastAttempt != null &&
          DateTime.now().difference(lastAttempt) < const Duration(minutes: 5)) {
        debugPrint('❌ Max retries reached for key: $key');
        _notifyAdUpdate(key, null);
        return;
      } else {
        // Reset retry count after cooldown
        _retryCount[key] = 0;
      }
    }

    // Load new ad
    await _loadNewAd(key, size, customAdUnitId);
  }

  /// Load a new banner ad
  Future<void> _loadNewAd(String key, Size size, String? customAdUnitId) async {
    _adStates[key] = AdLoadState.loading;
    _lastLoadAttempt[key] = DateTime.now();

    try {
      final adUnitId = customAdUnitId ?? getAdUnitId(key);
      final adSize = _getOptimalAdSize(size.width, size.height);

      debugPrint(
          '📱 Loading ad for key: $key with size: ${adSize.width}x${adSize.height}');

      final ad = BannerAd(
        adUnitId: adUnitId,
        size: adSize,
        request: const AdRequest(),
        listener: BannerAdListener(
          onAdLoaded: (ad) {
            debugPrint('✅ Ad loaded successfully for key: $key');
            _adStates[key] = AdLoadState.loaded;
            _retryCount[key] = 0;
            _notifyAdUpdate(key, ad as BannerAd);
          },
          onAdFailedToLoad: (ad, error) {
            debugPrint('❌ Ad failed to load for key: $key - ${error.message}');
            ad.dispose();
            _adStates[key] = AdLoadState.failed;
            _notifyAdUpdate(key, null);

            // Increment retry count and schedule retry
            _retryCount[key] = (_retryCount[key] ?? 0) + 1;
            if ((_retryCount[key] ?? 0) < maxRetries) {
              Timer(retryDelay * (_retryCount[key] ?? 1), () {
                debugPrint(
                    '🔄 Retrying ad load for key: $key (attempt ${_retryCount[key]})');
                _loadNewAd(key, size, customAdUnitId);
              });
            }
          },
          onAdOpened: (ad) => debugPrint('📱 Ad opened for key: $key'),
          onAdClosed: (ad) => debugPrint('📱 Ad closed for key: $key'),
          onAdImpression: (ad) => debugPrint('👁️ Ad impression for key: $key'),
          onAdClicked: (ad) => debugPrint('👆 Ad clicked for key: $key'),
        ),
      );

      await ad.load();
    } catch (e) {
      debugPrint('❌ Exception loading ad for key: $key - $e');
      _adStates[key] = AdLoadState.failed;
      _notifyAdUpdate(key, null);

      // Schedule retry
      _retryCount[key] = (_retryCount[key] ?? 0) + 1;
      if ((_retryCount[key] ?? 0) < maxRetries) {
        Timer(retryDelay * (_retryCount[key] ?? 1), () {
          _loadNewAd(key, size, customAdUnitId);
        });
      }
    }
  }

  /// Notify listeners of ad update
  void _notifyAdUpdate(String key, BannerAd? ad) {
    if (_adStreamControllers.containsKey(key)) {
      _adStreamControllers[key]!.add(ad);
    }
  }

  /// Preload ads for better performance
  Future<void> preloadAds(List<String> keys, Size size) async {
    debugPrint('🚀 Preloading ${keys.length} ads');

    // Ensure initialization before preloading
    await ensureInitialized();

    // Load ads with staggered approach
    for (int i = 0; i < keys.length; i++) {
      final key = keys[i];

      // Skip if already loaded or loading
      if (_adStates[key] == AdLoadState.loaded ||
          _adStates[key] == AdLoadState.loading) {
        continue;
      }

      // Start loading without waiting
      loadAd(key, size: size);

      // Small delay between loads
      if (i < keys.length - 1) {
        await Future.delayed(const Duration(milliseconds: 50));
      }
    }
  }

  /// Clean up resources for a specific key
  void cleanupAd(String key) {
    _adStates.remove(key);
    _lastLoadAttempt.remove(key);
    _retryCount.remove(key);

    if (_adStreamControllers.containsKey(key)) {
      _adStreamControllers[key]!.close();
      _adStreamControllers.remove(key);
    }
  }

  /// Clean up ads that are far from current position
  void cleanupDistantAds(String keyPrefix, int currentIndex) {
    final keysToRemove = <String>[];

    _adStates.forEach((key, state) {
      if (key.startsWith(keyPrefix)) {
        final match = RegExp(r'\d+$').firstMatch(key);
        if (match != null) {
          final adIndex = int.parse(match.group(0)!);
          // Remove ads that are more than 10 positions away
          if ((adIndex - currentIndex).abs() > 10) {
            keysToRemove.add(key);
          }
        }
      }
    });

    for (final key in keysToRemove) {
      cleanupAd(key);
    }

    if (keysToRemove.isNotEmpty) {
      debugPrint('🧹 Cleaned up ${keysToRemove.length} distant ads');
    }
  }

  /// Get ad state for debugging
  AdLoadState? getAdState(String key) => _adStates[key];

  /// Get cache statistics
  Map<String, dynamic> getCacheStats() {
    return {
      'initialized': _isInitialized,
      'totalAds': _adStates.length,
      'loadedAds':
          _adStates.values.where((s) => s == AdLoadState.loaded).length,
      'failedAds':
          _adStates.values.where((s) => s == AdLoadState.failed).length,
      'loadingAds':
          _adStates.values.where((s) => s == AdLoadState.loading).length,
    };
  }

  /// Set scroll state to prevent ad refresh during scrolling
  void setScrolling(bool isScrolling) {
    _isScrolling = isScrolling;

    if (isScrolling) {
      _scrollEndTimer?.cancel();
    } else {
      // Wait a bit after scroll ends before allowing refreshes
      _scrollEndTimer = Timer(const Duration(milliseconds: 1000), () {
        _isScrolling = false;
      });
    }
  }

  /// Start periodic refresh of ads (only when not scrolling)
  void _startPeriodicRefresh() {
    Timer.periodic(adRefreshInterval, (_) {
      // Don't refresh ads while user is scrolling to prevent jumps
      if (_isScrolling) {
        debugPrint('⏸️ Skipping ad refresh during scroll');
        return;
      }

      // Refresh ads that have been loaded for a while
      final now = DateTime.now();
      _lastLoadAttempt.forEach((key, loadTime) {
        if (_adStates[key] == AdLoadState.loaded &&
            now.difference(loadTime) > adRefreshInterval) {
          debugPrint('🔄 Refreshing ad for key: $key');
          // Trigger reload by clearing state
          _adStates[key] = AdLoadState.idle;
        }
      });
    });
  }

  @override
  void onClose() {
    // Cancel scroll timer
    _scrollEndTimer?.cancel();

    // Close all stream controllers
    for (final controller in _adStreamControllers.values) {
      controller.close();
    }
    _adStreamControllers.clear();
    super.onClose();
  }
}

/// Ad loading states
enum AdLoadState {
  idle,
  loading,
  loaded,
  failed,
}

/// Premium ad widget with automatic state management
class PremiumAdWidget extends StatefulWidget {
  final String adKey;
  final double width;
  final double height;
  final Widget? placeholder;
  final Widget? errorWidget;

  const PremiumAdWidget({
    Key? key,
    required this.adKey,
    required this.width,
    required this.height,
    this.placeholder,
    this.errorWidget,
  }) : super(key: key);

  @override
  State<PremiumAdWidget> createState() => _PremiumAdWidgetState();
}

class _PremiumAdWidgetState extends State<PremiumAdWidget>
    with AutomaticKeepAliveClientMixin {
  BannerAd? _currentAd;
  AdLoadState _state = AdLoadState.idle;
  StreamSubscription<BannerAd?>? _adSubscription;
  bool _isDisposed = false;

  @override
  bool get wantKeepAlive => false; // Don't keep alive to avoid reuse issues

  @override
  void initState() {
    super.initState();
    _setupAdListener();
    // Delay initial load to ensure proper initialization
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!_isDisposed) {
        _requestAd();
      }
    });
  }

  void _setupAdListener() {
    // Listen to ad updates for this key
    _adSubscription = PremiumAdManager.to.getAdStream(widget.adKey).listen(
      (ad) {
        if (!mounted || _isDisposed) return;

        setState(() {
          // Dispose previous ad if exists
          _currentAd?.dispose();
          _currentAd = ad;
          _state = ad != null ? AdLoadState.loaded : AdLoadState.failed;
        });
      },
    );
  }

  Future<void> _requestAd() async {
    if (!mounted || _isDisposed) return;

    setState(() {
      _state = AdLoadState.loading;
    });

    await PremiumAdManager.to.loadAd(
      widget.adKey,
      size: Size(widget.width, widget.height),
    );
  }

  @override
  void dispose() {
    _isDisposed = true;
    _adSubscription?.cancel();
    _currentAd?.dispose();
    // Don't cleanup the ad manager entry to allow reuse
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color:
            _state == AdLoadState.loaded ? Colors.transparent : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: _buildContent(),
      ),
    );
  }

  Widget _buildContent() {
    switch (_state) {
      case AdLoadState.idle:
      case AdLoadState.loading:
        return widget.placeholder ?? _buildPremiumPlaceholder();

      case AdLoadState.loaded:
        if (_currentAd != null) {
          return _buildAdWithAnimation();
        }
        return widget.errorWidget ?? _buildPremiumError();

      case AdLoadState.failed:
        return widget.errorWidget ?? _buildPremiumError();
    }
  }

  Widget _buildAdWithAnimation() {
    return AnimatedOpacity(
      opacity: 1.0,
      duration: const Duration(milliseconds: 500),
      child: AdWidget(ad: _currentAd!),
    );
  }

  Widget _buildPremiumPlaceholder() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.grey[100]!,
            Colors.grey[200]!,
          ],
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 32,
              height: 32,
              child: CircularProgressIndicator(
                strokeWidth: 3.0,
                valueColor: AlwaysStoppedAnimation<Color>(
                  Colors.grey[400]!,
                ),
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Učitavanje reklame...',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 13,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPremiumError() {
    return GestureDetector(
      onTap: () {
        // Retry loading on tap
        if (mounted && !_isDisposed) {
          _requestAd();
        }
      },
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.grey[50]!,
              Colors.grey[100]!,
            ],
          ),
        ),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.refresh_rounded,
                size: 36,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 12),
              Text(
                'Dodirnite za učitavanje',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 13,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Sadržaj se učitava',
                style: TextStyle(
                  color: Colors.grey[500],
                  fontSize: 11,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
